package com.ecco.infrastructure.config.root;

import com.ecco.event.CacheEvictAgent;
import com.ecco.infrastructure.bus.MessageBus;
import com.ecco.infrastructure.cachebust.EntityCacheBustKeyRepository;
import org.ehcache.config.CacheConfiguration;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.jsr107.Eh107Configuration;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.jcache.JCacheCacheManager;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.cache.CacheManager;
import javax.cache.Caching;
import javax.cache.spi.CachingProvider;

import static java.time.Duration.ofHours;
import static java.time.Duration.ofSeconds;
import static org.ehcache.config.builders.ExpiryPolicyBuilder.noExpiration;
import static org.ehcache.config.builders.ExpiryPolicyBuilder.timeToLiveExpiration;
import static org.ehcache.config.builders.ResourcePoolsBuilder.heap;


@Configuration
@EnableCaching
public class CacheConfig {

    public static final String CACHE_BUILDINGS = "buildings";
    public static final String CACHE_SR_TASK_COMMANDS = "serviceRecipientTaskCommands";
    public static final String CACHE_ENTITY_CACHEBUST_KEY = "entityCacheBustKey"; // See DEV-1747
    public static final String CACHE_ROTA_VIEW = "rotaController_view";
    public static final String CACHE_SVCCAT = "serviceCategoryCache";
    public static final String CACHE_SERVICE = "serviceCache";
    public static final String CACHE_PROJECT = "projectCache";

    public static final String[] CACHE_CONFIGS = {"idNames","listDefinitionsById","settings","markdownAsHtmlCache","taskDefinitionService",
            "serviceTypeService","serviceTypeViewModel","outcomeViewModels","riskAreaViewModels","questionGroupViewModels","softwareModuleService",
            "serviceRecipientTaskCommands","softwareFeatureDefaultVote"};

    // NB /config/user/ is just cached via http
    public static final String[] CACHE_ADMINS = {"idNames","listDefinitionsById", CACHE_SVCCAT, CACHE_SERVICE, CACHE_PROJECT, CACHE_BUILDINGS,
            "aclCache", "aclVisibilityService", "aclByGroups"};

    @Value("${cache.cosmoScaleFactor:1}")
    int cacheCosmoScaleFactor;

    @Value("${cache.aclScaleFactor:2}")
    int cacheAclScaleFactor;

    /** Max heap per cache I believe - so lots of default cache */
//    @Value("${cache.maxHeapMB:2}")
//    private int maxHeapMB;

    @Value("${cache.maxEntriesLocalHeap:20000}")
    private int maxEntriesLocalHeap;

    @Bean
    public JCacheCacheManager cacheManager() {
        // WARNING: .create() will blindly go forth and ignore your config if a singleton already exists
        // You must ensure this runs before Hibernate kicks off
        CachingProvider provider = Caching.getCachingProvider();
        CacheManager cacheManager = provider.getCacheManager();
        if (!cacheManager.getCacheNames().iterator().hasNext()) { // TODO: This was added to avoid situation where we seem to create caches twice for different test contexts in CI run
            configureCaches(cacheManager);

            cacheManager.getCacheNames().forEach(n -> cacheManager.enableManagement(n, true));
            cacheManager.getCacheNames().forEach(n -> cacheManager.enableStatistics(n, true));
        }


//                .timeToIdleSeconds(300)
// Removed as seems to have perf impact calculating sizes (try enabling it and see!)
//                .maxBytesLocalHeap(maxHeapMB, MemoryUnit.MEGABYTES)
//                .maxEntriesLocalHeap(maxEntriesLocalHeap * cacheCosmoScaleFactor)
//                .memoryStoreEvictionPolicy(LRU);
        return new JCacheCacheManager(cacheManager);
    }


    /**
     * Stats are dumped to log when we clear caches: @see com.ecco.webApi.serviceConfig.CacheCommandHandler.clearCaches
     */
    private void configureCaches(CacheManager cacheManager) {
        cacheManager.createCache("appointmentTypesById", eternalConfig());
        cacheManager.createCache(CACHE_BUILDINGS, eternalConfig());
        cacheManager.createCache(CACHE_ENTITY_CACHEBUST_KEY, eternalConfig());
        cacheManager.createCache(CACHE_ROTA_VIEW, eternalConfig(5000 * cacheCosmoScaleFactor)); //// Approx 100-200 bytes per entry so around 1Mb * cacheCosmoScaleFactor

        cacheManager.createCache("currentUserWorkers", eternalConfig()); // unused
        cacheManager.createCache("settings", eternalConfig()); // 500 bytes each
                // TODO .cache(eternalConfig("com.ecco.config.dom.Setting")) // Use Hib caching because Spring caching + Configurables breaks EhCache sizeof - could use the @IgnoreSizeOf annotation
        cacheManager.createCache("idNames", eternalConfig());
        cacheManager.createCache("listDefinitionsById", defaultConfig()); // around 50kB
        cacheManager.createCache("markdownAsHtmlCache", eternalConfig());
        cacheManager.createCache("menuService", eternalConfig()); // unused
        cacheManager.createCache("taskDefinitionService", eternalConfig());
        cacheManager.createCache("serviceRecipientSummary", eternalConfig());
        cacheManager.createCache("serviceTypeService", eternalConfig());
        cacheManager.createCache("serviceTypeViewModel", eternalConfig());  // 12kB
        cacheManager.createCache("outcomeViewModels", eternalConfig());
        cacheManager.createCache("riskAreaViewModels", eternalConfig());     // 15 kB
        cacheManager.createCache("questionGroupViewModels", eternalConfig()); // TODO: 250kB per group - need to store as JSON
        cacheManager.createCache("softwareModuleService", eternalConfig());
        cacheManager.createCache("aclCache", aclConfig()); // guessing WHAT WAS IT BEFORE??  size: approx 500 bytes
        cacheManager.createCache("aclVisibilityService", aclConfig());
        cacheManager.createCache("aclByGroups", aclConfig()); // NB this wasn't part of the fix
        cacheManager.createCache(CacheConfig.CACHE_SVCCAT, defaultConfig(4000));
        cacheManager.createCache(CacheConfig.CACHE_SERVICE, defaultConfig(2000));
        cacheManager.createCache(CacheConfig.CACHE_PROJECT, defaultConfig(2000));
        cacheManager.createCache("softwareFeatureDefaultVote", defaultConfig()); // < 1kB each
        cacheManager.createCache(CACHE_SR_TASK_COMMANDS, eternalConfig()); // eternal config is 1000 elements
        cacheManager.createCache("globalConfig", eternalConfig(1)); // only 1 element, - needs approx 500kB now that we cache as UTF-8 bytes not Java String
        cacheManager.createCache("publicStat", eternalConfig(1)); // only 1 element
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibUser", cosmoConfig(500)); // Approx 2kB each
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibItem", cosmoConfig(10000));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibAttribute", cosmoConfig(2000));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibDictionaryAttribute.value", cosmoConfig(500));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibMultiValueStringAttribute.value", cosmoConfig(1000));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibItem.attributes", cosmoConfig(10000)); // Does get hit on recent test with rota on test4
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibItem.stamps", cosmoConfig(20000));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibNoteItem.modifications", cosmoConfig(1000));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibCollectionItemDetails", cosmoConfig(1000));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibItem.parentDetails", cosmoConfig(1000));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibStamp", cosmoConfig(10000));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibCollectionSubscription", cosmoConfig(1000));
        cacheManager.createCache("org.osaf.cosmo.model.hibernate.HibUser.subscriptions", cosmoConfig(1000));
        cacheManager.createCache("org.hibernate.cache.StandardQueryCache", cosmoConfig(2000));
        cacheManager.createCache("org.hibernate.cache.UpdateTimestampsCache", cosmoConfig(5000));
        cacheManager.createCache("com.ecco.serviceConfig.dom.Action", cosmoConfig(5000));  // 1500 bytes each
        cacheManager.createCache("com.ecco.security.dom.User", cosmoConfig(5000));
    }

    private javax.cache.configuration.Configuration<Object, Object> eternalConfig(int maxEntriesHeap) {
        var configuration = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(Object.class, Object.class, heap(maxEntriesHeap))
                .withExpiry(noExpiration())
                .build();
        return Eh107Configuration.fromEhcacheCacheConfiguration(configuration);
    }

    private javax.cache.configuration.Configuration<Object, Object> eternalConfig() {
        return eternalConfig(1000);
    }

    private javax.cache.configuration.Configuration<Object, Object> defaultConfig(int maxEntriesHeap) {
        CacheConfiguration<Object, Object> configuration = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(Object.class, Object.class, heap(maxEntriesHeap))
                .withExpiry(timeToLiveExpiration(ofSeconds(300)))
                .build();
        return Eh107Configuration.fromEhcacheCacheConfiguration(configuration);
    }

    private javax.cache.configuration.Configuration<Object, Object> defaultConfig() {
        return defaultConfig(1000);
    }

    private javax.cache.configuration.Configuration<Object, Object> aclConfig() {
        CacheConfiguration<Object, Object> configuration = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(Object.class, Object.class, heap(1000L * cacheAclScaleFactor))
                // we will struggle every so many hours, but we need to keep the problem present
                .withExpiry(timeToLiveExpiration(ofHours(2L * cacheAclScaleFactor)))
                //.withExpiry(noExpiration()) // NB have to clear cache to get permission updates, else all cache cleared together
                .build();
        return Eh107Configuration.fromEhcacheCacheConfiguration(configuration);
    }

    /** TTL TTI are as cosmo specified - not specific to cosmo though */
    private javax.cache.configuration.Configuration<Object, Object> cosmoConfig(int maxElements) {
        CacheConfiguration<Object, Object> configuration = CacheConfigurationBuilder
                .newCacheConfigurationBuilder(Object.class, Object.class, heap(maxElements * cacheCosmoScaleFactor))
                .withExpiry(timeToLiveExpiration(ofSeconds(600)))
                .build();
        // Was also this but probably doesn't matter        .timeToIdleSeconds(300)
        return Eh107Configuration.fromEhcacheCacheConfiguration(configuration);
    }


    @Bean
    public CacheEvictAgent cacheEvictAgent(org.springframework.cache.CacheManager cacheManager, MessageBus<ApplicationEvent> messageBus) {
        return new CacheEvictAgent(cacheManager, messageBus);
    }

    @Bean
    EntityCacheBustKeyRepository entityCacheBustKeyRepository() {
        return new EntityCacheBustKeyRepository();
    }
}
