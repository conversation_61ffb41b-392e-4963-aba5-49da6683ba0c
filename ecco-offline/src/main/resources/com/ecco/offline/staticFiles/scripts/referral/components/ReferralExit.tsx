import * as React from "react"
import {FC} from "react"
import {EccoDate, EccoDateTime, IdNameDisabled} from "@eccosolutions/ecco-common";
import {Alert, Grid} from "@eccosolutions/ecco-mui";

import {CommandQueue, CommandSource, ReferralTaskExitCommand} from "ecco-commands";
import {
    checkBox,
    CommandSubform,
    datePickerIso8601Input,
    dropdownList, LoadingOrError,
    ModalCommandForm, ServiceRecipientWithEntitiesContext,
    textArea,
    useCurrentServiceRecipientWithEntities, usePromise, useServicesContext
} from "ecco-components";
import {ConfigResolver, EXITREASON_LISTNAME, PrefixType, SessionData, TaskNames} from "ecco-dto";
import {TaskWithTitle} from "ecco-dto/workflow-dto";
import {ExitedSummaryFields} from "ecco-dto/referral-dto";
import {getReferralRepository} from "ecco-offline-data";

// NB needs full referral for exitedComment
function useExitComment(props: {context: ServiceRecipientWithEntitiesContext}) {
    let commentQ: Promise<string>;
    switch (props.context.serviceRecipient.prefix) {
        case "r":
            commentQ = props.context.referral.exitReasonId // if there is a reason, lets load the comment
                    ? getReferralRepository().findOneReferralByServiceRecipientId(props.context.referral.serviceRecipientId)
                            .then(r => r.exitComment)
                    : Promise.resolve(null);
            break;
        case "i":
            commentQ = Promise.resolve(props.context.incident.signpostedExitComment);
            break;
        case "m":
            commentQ = Promise.resolve(props.context.repair.signpostedExitComment);
            break;
        // NB worker not applicable for ServiceRecipientCaseStatusUpdate
        // case "w":
        //     commentQ = Promise.resolve(props.context.workerJob.signpostedExitComment);
        //     break;
    }

    const output = usePromise(() => commentQ,
    []
    );

    return output;
}

interface IncidentDetailsDto {
    reviewDate?: string;
}
interface RepairDetailsDto {
    reviewDate?: string;
}
interface CommonExitDto extends ExitedSummaryFields {
    serviceRecipientId: number;
    exitComment: string;
    receivingServiceDate: string;
    decisionDate: string;
    receivedDate: string;
    incidentDetails?: IncidentDetailsDto;
    repairDetails?: RepairDetailsDto;
}

const IncidentNames = {
    "exited": "close",
    "exit reason": "close reason"
}
const DefaultNames = {
    "exited": "exited",
    "exit reason": "exit reason"
}
type FieldNames = keyof typeof DefaultNames;

function lookupMessages(prefix: PrefixType) {
    switch (prefix) {
        case "i":
            return IncidentNames;
        default:
            return DefaultNames;
    }
}

const ReferralExitDialog: FC<{serviceRecipientId: number; task: TaskWithTitle; onSaved: () => void}> = ({serviceRecipientId, task, onSaved}) => {

    const {resolved: context, reload: reloadSRWE} = useCurrentServiceRecipientWithEntities()
    const {resolved: comment, loading, error} = useExitComment({context})

    if (loading) {
        return <LoadingOrError error={error}/>;
    }

    let dtoExit: CommonExitDto;
    switch (context.serviceRecipient.prefix) {
        case "i":
            dtoExit = {
                serviceRecipientId: context.serviceRecipient.serviceRecipientId,
                exitComment: comment,
                receivingServiceDate: undefined,
                decisionDate: undefined,
                receivedDate: context.incident.receivedDate,
                exitedDate: context.incident.exitedDate,
                exitReasonId: context.incident.exitReasonId,
                incidentDetails: {reviewDate: context.incident.reviewDate}
            };
            break;

        case "m":
            dtoExit = {
                serviceRecipientId: context.serviceRecipient.serviceRecipientId,
                exitComment: comment,
                receivingServiceDate: undefined,
                decisionDate: undefined,
                receivedDate: context.repair.receivedDate,
                exitedDate: context.repair.exitedDate,
                exitReasonId: context.repair.exitReasonId,
                repairDetails: {reviewDate: context.repair.reviewDate}
            };
            break;

        default:
            dtoExit = {
                serviceRecipientId: context.serviceRecipient.serviceRecipientId,
                exitComment: comment,
                receivingServiceDate: context.referral.receivingServiceDate,
                decisionDate: context.referral.decisionDate,
                receivedDate: context.referral.receivedDate,
                exitedDate: context.referral.exitedDate,
                exitReasonId: context.referral.exitReasonId,
            };
    }

    return (
        <ModalCommandForm
            show={true}
            setShow={() => onSaved()}
            title={"close"}
            action="save"
            maxWidth="sm"
            afterSave={() => {reloadSRWE();}}
        >
            {form => <ReferralExitSubform
                    type={context.serviceRecipient.prefix}
                    dto={dtoExit}
                    configResolver={context.serviceRecipient.configResolver}
                    sessionData={context.serviceRecipient.features}
                    taskHandle={task.taskHandle}
                    commandForm={form}
            />}
        </ModalCommandForm>);
};

interface Props {
    type: PrefixType;
    dto: CommonExitDto;
    configResolver: ConfigResolver;
    sessionData: SessionData;
    taskHandle: string;
}

interface State {
    exitDate: string;
    exitReasonId: number;
    exitComment: string;
    undoCloseOff: boolean;
    incidentDetails: IncidentDetailsDto
}

function isEmpty(str: any) {
    return !str;
}

export const IncidentFields: FC<{dto: IncidentDetailsDto | null, setter: (details: IncidentDetailsDto) => void}> = props => {
    const {sessionData} = useServicesContext();
    return <Grid item xs={12}>
        {datePickerIso8601Input<{reviewDate?: string}>("reviewDate", "review", state => props.setter(state), props.dto, undefined, false)}
    </Grid>
}

class ReferralExitSubform extends CommandSubform<Props, State> implements CommandSource {

    constructor(props) {
        super(props);
        this.state = {
            exitDate: this.props.dto.exitedDate,
            exitReasonId: this.props.dto.exitReasonId,
            exitComment: this.props.dto.exitComment,
            undoCloseOff: false,
            incidentDetails: {reviewDate: this.props.dto.incidentDetails?.reviewDate}
        };
    }

    emitChangesTo(commandQueue: CommandQueue) {
        const cmd = new ReferralTaskExitCommand(this.props.dto.serviceRecipientId, this.props.taskHandle);

        if (this.state.undoCloseOff) {
            cmd.changeExitedUndoCloseOff();
        } else {
            cmd.changeExitedDate(EccoDate.parseIso8601(this.props.dto.exitedDate), EccoDate.parseIso8601(this.state.exitDate));
            cmd.changeExitedReason(this.props.dto.exitReasonId, this.state.exitReasonId);
            cmd.changeExitedComment(this.props.dto.exitComment, this.state.exitComment);
            if (this.props.dto.incidentDetails) {
                cmd.changeReviewDate(this.props.dto.incidentDetails.reviewDate, this.state.incidentDetails.reviewDate)
            }
        }

        if (cmd.hasChanges()) {
            commandQueue.addCommand(cmd);
        }
    }

    getErrors(): string[] {
        return ['exitDate', 'exitReasonId'].reduce( (errors, field) => {
            if (!this.state.undoCloseOff) {
                if (isEmpty(this.state[field])) {
                    errors.push(`${field} is required`);
                } else if (field == 'exitDate') {
                    const exited = EccoDate.parseIso8601(this.state.exitDate)
                    // due to legacy or optional data, we could have missing info for validation
                    // so we test all of them as optional - receivingService -> decision made on (service+referral) -> received date
                    const receivingService = EccoDate.parseIso8601(this.props.dto.receivingServiceDate)
                    const interviewDateTime = EccoDateTime.parseIso8601(this.props.dto.decisionDate)
                    // could also do 'decided on' being the 'decisionReferralMadeOn' and 'decisionMadeOn'
                    const receivedDate = EccoDate.parseIso8601(this.props.dto.receivedDate)
                    if (receivingService && exited.earlierThan(receivingService)) {
                        errors.push(`exited date can't be before start`);
                    } else if (interviewDateTime && exited.addDays(1).toDateTimeMidnight().earlierThan(interviewDateTime)) {
                        errors.push(`exited date can't be before interview`);
                    } else if (receivedDate && exited.earlierThan(receivedDate)) {
                        errors.push(`exited date can't be before received`);
                    }
                }
            }
            return errors;
        }, []);
    }

    render() {

        let stateSetter = state => this.setState(state);
        const messages = (field: FieldNames) => lookupMessages(this.props.type)[field];

        // getExitReasonList
        // non-alphabetical list, but perhaps list defs should have an order column
        const exitListName = this.props.configResolver.getServiceType().getTaskDefinitionSetting(TaskNames.close, "exitListName")
                || EXITREASON_LISTNAME;
        const exitList = this.props.sessionData.getListDefinitionEntriesByListName(exitListName, undefined, this.props.dto?.exitReasonId);

        return <Grid container>
                {/*{this.props.type == "i" &&
                    <IncidentFields dto={this.state.incidentDetails} setter={dto => this.setState({incidentDetails: {...this.state.incidentDetails, ...dto}})} />
                }*/}
                <Grid item xs={12}>
                    {datePickerIso8601Input("exitDate", messages("exited"), state => this.setState(state), this.state, undefined, true)}
                </Grid>
                <Grid item xs={12}>
                    {dropdownList(messages("exit reason"), state => this.setState(state), this.state, "exitReasonId",
                      exitList.map(ld => ({
                          id: ld.getId(),
                          name: ld.getName(),
                          disabled: ld.getDisabled()
                      } as IdNameDisabled)), {}, undefined, true)}
                </Grid>
                <Grid item xs={12}>
                    {textArea("exitComment", "comment", stateSetter, this.state)}
                </Grid>
                {this.props.sessionData.hasRoleReferralAdmin() && this.props.dto.exitedDate &&
                    <Grid item xs={12}>
                        <div style={{textAlign: "center"}}>
                            {checkBox("undoCloseOff","undo close", stateSetter, this.state)}
                        </div>
                    </Grid>
                }
                {this.props.sessionData.isModuleEnabled("rota") &&
                    !this.props.dto.exitedDate && this.state.exitDate && <Grid item xs={12}>
                    <Alert severity="info">
                        Note: Any appointments schedules that extend beyond {EccoDate.iso8601ToFormatShort(this.state.exitDate)} will
                        be shortened to this date, and any appointments after that will be cancelled.
                    </Alert>
                </Grid>}
            </Grid>;
    }

}
export default ReferralExitDialog;