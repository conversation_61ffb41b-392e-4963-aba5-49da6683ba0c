import $ = require("jquery");
import Form = require("../../controls/Form");
import SelectList = require("../../controls/SelectList");
import ServicesProjects = require("./ServicesProjects");
import {ActionState} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import {SessionDataAjaxRepository} from "ecco-dto";
import * as aclDto from "ecco-dto/acl-dto";
import {PersonUserSummary} from "ecco-dto/contact-dto";
import DialogContent from "../../controls/DialogContent";
import {IndividualAjaxRepository} from "ecco-dto";
import {CommandAjaxRepository, UserAclChangeCommand} from 'ecco-commands';

const sessionDataRepository = new SessionDataAjaxRepository(apiClient);
var individualRepository = new IndividualAjaxRepository(apiClient);
const commandRepository = new CommandAjaxRepository(apiClient);

var SERVICE_CLASS = "com.ecco.dom.ServiceAclId";
var PROJECT_CLASS = "com.ecco.dom.ProjectAclId";


class AclEntryControl implements DialogContent {

    private $container = $("<div>");
    private form = new Form();
    private servicesProjects: ServicesProjects;
    private userSelectList = new SelectList("user-select", false);
    private initialServices: number[] | undefined;
    private initialProjects: number[] | undefined;

    constructor(private userId: number, restrictToUserSvcCat: boolean,
                private afterSubmitted: () => void,
                aclEntries?: aclDto.AclEntryDto[]) {

        if (aclEntries) {
            this.initialServices = aclEntries.filter(acl => acl.clazz == SERVICE_CLASS).map(acl => acl.secureObjectId);
            this.initialProjects = aclEntries.filter(acl => acl.clazz == PROJECT_CLASS).map(acl => acl.secureObjectId);
        }
        this.servicesProjects = new ServicesProjects(sessionDataRepository,
                                                     restrictToUserSvcCat,
                                                     this.initialServices,
                                                     this.initialProjects );
        this.layoutForm();
        if (!userId) {
            this.populateIndividualUsers();
        }
    }

    private layoutForm() {

        if (!this.userId) {
            var $ddlUsr = this.userSelectList.element();
            var $rowUsr = $("<div>").attr("class", "e-row");
            $("<span>").attr("class", "e-label").text("user ").appendTo($rowUsr);
            $("<span>").attr("class", "e-input").append($ddlUsr).appendTo($rowUsr);
            this.form.append($rowUsr);
        }

        var $rowSrv = $("<div>");
        this.servicesProjects.attachTo($rowSrv);
        this.form.append($rowSrv);

        this.$container.append(this.form.element());
    }

    private populateIndividualUsers() {
        individualRepository.findIndividualsWithAuthority("ROLE_STAFF").then(
            (results: PersonUserSummary[]) => this.createIndividualUsersList(results));
    }

    private createIndividualUsersList(results: PersonUserSummary[]) {
        this.userSelectList.change((id) => {});

        this.userSelectList.populateFromList(results,
            (personUser) => ({ key: personUser.username, value: personUser.username}));
    }

    public submitForm(): Promise<void> {
        var serviceIds = this.servicesProjects.getSelectedServices();
        var projectIds = this.servicesProjects.getSelectedProjects();
        var username = this.userId || this.userSelectList.selected(true);
        if (!username) {
            throw new Error("validation error");
        }

        const cmd = new UserAclChangeCommand(this.userId)
            .changeServices(this.initialServices, serviceIds)
            .changeProjects(this.initialProjects, projectIds);

        return commandRepository.sendCommand(cmd)
            .then( () => {
                if (this.afterSubmitted) {
                    this.afterSubmitted();
                }
            })
            .catch( (e) => {
                alert("failed to save"); // e.reason.message if wanted to expose to user
                throw e; // allows ActionButton .fail to log the error on console and throw for NR
            });
    }

    public element() {
        return this.form.element();
    }

    public getFooter() {
        return null;
    }

    getModalStyle() {
        return "modal-full";
    }

    getTitle(): string {
        return "permissions";
    }

    registerActionsChangeListener(updateActions: (actions: ActionState[]) => void): void {
        updateActions([{
            label: 'done',
            onClick: () => {
                this.submitForm();
                return Promise.resolve();
            }
        }])
    }

    setOnFinished(callback: () => void): void {
    }

}

export = AclEntryControl;
