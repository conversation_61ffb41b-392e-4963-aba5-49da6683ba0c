import $ = require("jquery");
import SelectList = require("../../controls/SelectList");
import TextAreaInput = require("../../controls/TextAreaInput");
import SessionDataService = require("../../feature-config/SessionDataService");
import AclsBulkImportDisplayConverter = require("./AclsBulkImportDisplayConverter");
import AclsControl = require("./AclsControl");
import {apiClient} from "ecco-components";
import {AclAjaxRepository} from "../../acls/AclAjaxRepository";

const aclRepository = new AclAjaxRepository(apiClient);
const config = SessionDataService.getFeatures();

const serviceList = new SelectList("servicesList");
const serviceUsernames = new TextAreaInput("servicesUsernamesText");
const projectList = new SelectList("projectsList");
const projectUsernames = new TextAreaInput("projectsUsernamesText");

class AclsView {

    private aclsBulkImportDisplayConverter: AclsBulkImportDisplayConverter;

    public init() {
        this.setupHiddenBulkImportServicesAndProjects();
        this.setupHiddenApplyAclsButton();
        this.ensureAcls();
        this.setupAclControl();
    }

    private ensureAcls() {
        aclRepository.ensureAcls()
            .catch( (reason) => {alert("failed: " + reason.reason.message)} );
    }

    private setupAclControl() {
        const aclsControl = new AclsControl("aclContainer");
        aclsControl.load();
    }

    /**
     * DEL ME: we should delete this method and jsp fragments
     * but there may be mileage in retaining it secretly for a while
     */
    public setupHiddenBulkImportServicesAndProjects() {

        $("#services").append(serviceList.element());
        $("#projects").append(projectList.element());

        config
            .then( config => {
                serviceList.populateFromList(config.getDto().services, (service) => {
                    return {key: service.id.toString(), value: service.name};
                });

                projectList.populateFromList(config.getDto().projects, (project) => {
                    return {key: project.id.toString(), value: project.name};
                });
            });

        // usernames text area

        const $servicesUsernames = $("#servicesUsernames");
        $servicesUsernames.append(serviceUsernames.element());
        // appending trigger
        const $appendServicesUsernames: $.JQuery = $("<button>").text("append service/users");
        $appendServicesUsernames.click((event: $.JQueryMouseEventObject) => {
            // get the service/usernames
            const serviceId = serviceList.selected(false);
            const usernamesTxt = serviceUsernames.val();
            this.aclsBulkImportDisplayConverter.appendAclInputToDisplay(usernamesTxt, serviceId, "com.ecco.dom.ServiceAclId");
        });
        $servicesUsernames.append($appendServicesUsernames);

        // usernames text area
        const $projectsUsernames = $("#projectsUsernames");
        $projectsUsernames.append(projectUsernames.element());
        // appending trigger
        const $appendProjectsUsernames: $.JQuery = $("<button>").text("append projects/users");
        $appendProjectsUsernames.click((event: $.JQueryMouseEventObject) => {
            // get the service/usernames
            const projectId = projectList.selected(false);
            const usernamesTxt = projectUsernames.val();
            this.aclsBulkImportDisplayConverter.appendAclInputToDisplay(usernamesTxt, projectId, "com.ecco.dom.ProjectAclId");
        });
        $projectsUsernames.append($appendProjectsUsernames);
    }

    /**
     * DEL ME: we should delete this method and jsp fragments
     * but there may be mileage in retaining it secretly for a while
     */
    private setupHiddenApplyAclsButton() {
        const $ensure: $.JQuery = $("<button>").text("apply acls");
        $ensure.click((event: $.JQueryMouseEventObject) => {
            $("#result").text("");
            aclRepository.applyEntries(this.aclsBulkImportDisplayConverter.extractAcls(), [])
                .catch( (reason) => {alert("failed: " + reason.reason.message)} );
        });
        $("#applyAcls").append($ensure);
    }

}

export = AclsView
